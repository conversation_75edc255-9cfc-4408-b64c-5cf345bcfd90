import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Stack,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  Radio,
  Divider,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { Category } from 'src/services/api/use-categories-api';

interface MoveCategoryDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (destinationCategoryId: number) => void;
  sourceCategory: Category | null;
  availableCategories: Category[];
  isMoving?: boolean;
}

export default function MoveCategoryDialog({
  open,
  onClose,
  onConfirm,
  sourceCategory,
  availableCategories,
  isMoving = false,
}: MoveCategoryDialogProps) {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);

  // Filter out the source category from available options
  const filteredCategories = availableCategories.filter(
    (category) => sourceCategory && category.id !== sourceCategory.id
  );

  const handleConfirm = () => {
    if (selectedCategoryId) {
      onConfirm(selectedCategoryId);
    }
  };

  const handleClose = () => {
    setSelectedCategoryId(null);
    onClose();
  };

  const selectedCategory = filteredCategories.find(cat => cat.id === selectedCategoryId);

  // Don't render if no source category
  if (!sourceCategory) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxWidth: 500,
        },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h4">Move Content</Typography>
          <IconButton onClick={handleClose} size="small">
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          <Box>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Move all content from <strong>{sourceCategory.name}</strong> to:
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Select a destination category below. All agents and content will be moved to the selected category.
            </Typography>
          </Box>

          <Divider />

          {filteredCategories.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body2" color="text.secondary">
                No other categories available to move content to.
              </Typography>
            </Box>
          ) : (
            <List sx={{ maxHeight: 300, overflow: 'auto' }}>
              {filteredCategories.map((category) => (
                <ListItem key={category.id} disablePadding>
                  <ListItemButton
                    onClick={() => setSelectedCategoryId(category.id)}
                    selected={selectedCategoryId === category.id}
                    sx={{
                      borderRadius: 1,
                      mb: 0.5,
                      '&.Mui-selected': {
                        bgcolor: 'primary.lighter',
                        '&:hover': {
                          bgcolor: 'primary.lighter',
                        },
                      },
                    }}
                  >
                    <ListItemIcon>
                      <Radio
                        checked={selectedCategoryId === category.id}
                        onChange={() => setSelectedCategoryId(category.id)}
                        size="small"
                      />
                    </ListItemIcon>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          bgcolor: category.theme || 'primary.main',
                          color: 'white',
                        }}
                      >
                        <Iconify icon={category.icon || 'eva:folder-fill'} width={16} height={16} />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={category.name}
                      secondary={`${category.agentsCount || 0} agents`}
                      primaryTypographyProps={{
                        variant: 'subtitle2',
                        fontWeight: selectedCategoryId === category.id ? 600 : 400,
                      }}
                      secondaryTypographyProps={{
                        variant: 'caption',
                        color: 'text.secondary',
                      }}
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Stack direction="row" spacing={2} sx={{ width: '100%', justifyContent: 'flex-end' }}>
          <AppButton
            variant="outlined"
            color="inherit"
            label="Cancel"
            onClick={handleClose}
            sx={{
              height: '40px',
              px: 3,
              borderRadius: 2,
              textTransform: 'none',
            }}
          />
          <AppButton
            variant="contained"
            color="primary"
            label="Move Content"
            onClick={handleConfirm}
            disabled={!selectedCategoryId || filteredCategories.length === 0}
            isLoading={isMoving}
            sx={{
              height: '40px',
              px: 3,
              borderRadius: 2,
              textTransform: 'none',
            }}
          />
        </Stack>
      </DialogActions>
    </Dialog>
  );
}
