import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Stack } from '@mui/material';

import { Form } from 'src/components/hook-form/form-provider';
import { Field } from 'src/components/hook-form/fields';

import { categorySchema, CategoryFormValues } from './category-schema';
import IconSelector from './icon-selector';
import ColorSelector from './color-selector';

interface CategoryFormProps {
  onSubmit: (data: CategoryFormValues) => void;
  defaultValues?: Partial<CategoryFormValues>;
}

export default function CategoryForm({
  onSubmit,
  defaultValues,
}: CategoryFormProps) {
  const methods = useForm<CategoryFormValues>({
    mode: 'onChange',
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: '',
      description: '',
      icon: '',
      colorType: 'primary',
      customColor: '#FF5733',
      ...defaultValues,
    },
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = methods;

  const renderForm = (
    <Stack spacing={4}>
      {/* Category Name */}
      <Stack spacing={1}>
        <Field.Text
          name="name"
          label="Category Name"
          placeholder="Type your category name"
          InputLabelProps={{ shrink: true }}
          onKeyDown={(e) => {
            // Prevent form submission on Enter key
            if (e.key === 'Enter') {
              e.preventDefault();
            }
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            },
          }}
        />
      </Stack>

      {/* Description */}
      <Stack spacing={1}>
        <Field.Text
          name="description"
          label="Description"
          placeholder="Type your category description"
          multiline
          InputLabelProps={{ shrink: true }}
          onKeyDown={(e) => {
            // For multiline, allow Enter for new lines, but prevent form submission on Ctrl+Enter
            if (e.key === 'Enter' && e.ctrlKey) {
              e.preventDefault();
            }
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            },
          }}
        />
      </Stack>

      {/* Choose Icon */}
      <Stack sx={{width:'20%'}} spacing={1}>
        <IconSelector />
      </Stack>

      {/* Choose Color */}
      <Stack sx={{width:'20%'}} spacing={1}>
        <ColorSelector
          control={control}
          error={!!errors.colorType}
          helperText={errors.colorType?.message}
        />
      </Stack>


    </Stack>
  );

  return (
    <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
      {renderForm}
    </Form>
  );
}
