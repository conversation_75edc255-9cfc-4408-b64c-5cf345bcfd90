import React from 'react';
import { Typography, Box, Stack } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { ConfirmDialog } from 'src/components/custom-dialog';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  categoryName: string;
  isDeleting?: boolean;
}

export default function DeleteConfirmationDialog({
  open,
  onClose,
  onConfirm,
  categoryName,
  isDeleting = false,
}: DeleteConfirmationDialogProps) {
  return (
    <ConfirmDialog
      open={open}
      onClose={onClose}
      close={onClose}
      title={
        <Typography variant="h3" textAlign="center">
          Delete Category?
        </Typography>
      }
      content={
        <Typography variant="body1" textAlign="center">
          Are you sure you want to delete <strong>{categoryName}</strong>?
          <br />
          This action cannot be undone.
        </Typography>
      }
      icon={
        <Box sx={{ textAlign: 'center' }}>
          <Iconify
            icon="eva:alert-triangle-fill"
            sx={{ width: 64, height: 64, color: 'error.main' }}
          />
        </Box>
      }
      action={
        <Stack direction="row" justifyContent="center" spacing={2} sx={{ width: '100%' }}>
          <AppButton
            variant="outlined"
            color="inherit"
            label="Cancel"
            onClick={onClose}
            sx={{
              width: '45%',
              height: '48px',
              borderRadius: 2,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
            }}
          />
          <AppButton
            variant="contained"
            color="error"
            label="Confirm Delete"
            onClick={onConfirm}
            isLoading={isDeleting}
            sx={{
              width: '45%',
              height: '48px',
              borderRadius: 2,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
            }}
          />
        </Stack>
      }
    />
  );
}
