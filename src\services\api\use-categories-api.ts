import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for categories
export const categoryEndpoints = {
  list: '/categories',
  details: '/categories',
  moveContent: '/categories',
};

// Define the Category data type based on API response
export interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  theme: string;
  createdAt: string;
  updatedAt: string;
  // Additional properties for UI compatibility
  colorType?: 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error' | 'custom';
  customColor?: string;
  agentsCount?: number;
}

// Define the API response types
export interface CategoriesListResponse {
  categories: Category[];
  total: number;
}

// Define query parameters for categories list
export interface CategoriesQueryParams {
  take?: number;
  skip?: number;
  name?: string;
}

// Define the request body types
export interface CreateCategoryRequest {
  name: string;
  description: string;
  theme: string;
  icon: string;
}

export interface UpdateCategoryRequest {
  name: string;
  description: string;
  theme: string;
  icon: string;
}

export interface MoveCategoryContentRequest {
  destinationCategoryId: number;
}

// Create a hook to use the categories API
export const useCategoriesApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all categories
  const useGetCategories = (params?: CategoriesQueryParams) => {
    return apiServices.useGetListService<CategoriesListResponse, CategoriesQueryParams>({
      url: categoryEndpoints.list,
      params,
    });
  };

  // Get a single category by ID
  const useGetCategory = (id: number) => {
    return apiServices.useGetItemService<Category>({
      url: categoryEndpoints.details,
      id: id.toString(),
    });
  };

  // Create a new category
  const useCreateCategory = (onSuccess?: (data: Category) => void) => {
    return apiServices.usePostService<CreateCategoryRequest, Category>({
      url: categoryEndpoints.list,
      onSuccess,
      withFormData: false,
    });
  };

  // Update a category using PATCH
  const useUpdateCategory = (id: number, onSuccess?: () => void) => {
    return apiServices.usePatchService<UpdateCategoryRequest>({
      url: categoryEndpoints.details,
      id: id.toString(),
      onSuccess,
      withFormData: false,
      queryKey: categoryEndpoints.list + 'list',
    });
  };

  // Delete a category
  const useDeleteCategory = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<number>({
      url: categoryEndpoints.details,
      urlAfterSuccess: categoryEndpoints.list + 'list',
      onSuccess,
    });
  };

  // Move category content to another category
  const useMoveContent = (id: number, onSuccess?: () => void) => {
    return apiServices.usePatchService<MoveCategoryContentRequest>({
      url: categoryEndpoints.moveContent,
      id: `${id}/move-content`,
      onSuccess,
      withFormData: false,
      queryKey: categoryEndpoints.list + 'list',
    });
  };

  return {
    useGetCategories,
    useGetCategory,
    useCreateCategory,
    useUpdateCategory,
    useDeleteCategory,
    useMoveContent,
  };
};
