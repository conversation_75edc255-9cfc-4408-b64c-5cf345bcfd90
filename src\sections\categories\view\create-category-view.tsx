import { Box, Container, Typography, I<PERSON><PERSON><PERSON><PERSON>, Stack } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';

import { AppContainer, AppButton } from 'src/components/common';
import { Form } from 'src/components/hook-form/form-provider';
import { useTranslation } from 'react-i18next';
import CategoryForm from '../form/category-form';
import { useCategoriesView } from '../hooks/use-categories-view';
import { CategoryFormValues, categorySchema } from '../form/category-schema';

// ----------------------------------------------------------------------

const CreateCategoryView = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Use our custom hook for categories
  const { handleCreateCategory, isLoading } = useCategoriesView();

  // Handle form submission
  const handleFormSubmit = (data: CategoryFormValues) => {
    try {
      handleCreateCategory(data);
      // Navigate back to categories list on success
      navigate(paths.dashboard.categories.root);
    } catch (error) {
      console.error('Failed to create category:', error);
    }
  };

  // Handle cancel - navigate back to categories list
  const handleCancel = () => {
    navigate(paths.dashboard.categories.root);
  };

  // Form methods
  const methods = useForm<CategoryFormValues>({
    mode: 'onChange',
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: '',
      description: '',
      icon: '',
      colorType: 'primary',
      customColor: '#FF5733',
    },
  });

  const { handleSubmit, formState: { isSubmitting } } = methods;

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'grey.50',
        p: 3,
      }}
    >
      <Box sx={{ mb: 3 }}>
        <IconButton
          onClick={handleCancel}
          sx={{
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
        >
          <Iconify icon="eva:arrow-back-fill" />
          <Typography variant="body2" sx={{ ml: 1 }}>
            Back
          </Typography>
        </IconButton>
      </Box>

      <AppContainer
        title={t('categories.createNew')}
        pageTitle={t('categories.createNew')}
        routeLinks={[
          {
            name: t('categories.title'),
            href: paths.dashboard.categories.root,
          },
          {
            name: t('categories.createNew'),
          },
        ]}
      >
        <Box
          sx={{
            width: { xs: '100%', sm: '80%', md: '60%', lg: '60%' }, // Make it responsive
            mx: 'auto',
            my: '3%',
            bgcolor: 'divider',
            borderRadius: 2,
            p: 2,
            boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Container disableGutters maxWidth={false}>
            <Box sx={{ mb: 1 ,borderRadius:'50px'}}>
              <Typography color="rgba(15, 14, 17, 0.65)" variant="h4">
                Add your team&apos;s template details
                    </Typography>

            </Box>

            <Form methods={methods} onSubmit={handleSubmit(handleFormSubmit)}>
              <Box
                sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'divider',
                  backgroundColor: 'white',
                  borderRadius: 2,
                }}
              >
                <CategoryForm
                  onSubmit={handleFormSubmit}
                />

                {/* Create Button - positioned at bottom right */}
                <Stack direction="row" justifyContent="flex-end" sx={{ mt: 4 }}>
                  <AppButton
                    type="submit"
                    variant="contained"
                    color="primary"
                    isLoading={isSubmitting || isLoading}
                    label="Create"
                    sx={{
                      height: '48px',
                      px: 4,
                      borderRadius: 2,
                      textTransform: 'none',
                      fontSize: '1rem',
                      fontWeight: 500,
                    }}
                  />
                </Stack>
              </Box>
            </Form>
          </Container> {/* Closing Container tag */}
        </Box> {/* Closing Box tag for the centered form container */}

      </AppContainer> {/* Closing AppContainer tag */}
    </Box> /* Closing main Box tag */
  );
};

export default CreateCategoryView;